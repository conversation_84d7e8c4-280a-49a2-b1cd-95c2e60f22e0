# 互助会会員システム マイルストーン管理表

## プロジェクト概要
- **開始日**: 2025年9月1日
- **完了予定日**: 2028年9月1日
- **総期間**: 約36ヶ月（3年）
- **総工数**: 142人月
- **総予算**: 127,800,000円
- **インフラ**: IDCFクラウド

## 主要マイルストーン一覧

| No | マイルストーン | 完了予定日 | 期間 | 主要成果物 | 工数 | 予算(円) |
|----|---------------|------------|------|------------|------|----------|
| M1 | プロジェクト開始 | 2025年9月1日 | - | プロジェクト計画書 | - | - |
| M2 | 現行調査完了 | 2026年1月31日 | 5ヶ月 | 現行調査報告書 | 10人月 | 9,000,000 |
| M3 | RFP作成完了 | 2026年4月30日 | 3ヶ月 | RFP・システム要件書 | 6人月 | 5,400,000 |
| M4 | 提案書完了 | 2026年6月15日 | 1.5ヶ月 | システム提案書・見積書 | 1人月 | 900,000 |
| M5 | 要件定義完了 | 2026年11月15日 | 5ヶ月 | 要件定義書一式 | 10人月 | 9,000,000 |
| M6 | 基本設計完了 | 2027年4月15日 | 5ヶ月 | 基本設計書一式 | 10人月 | 9,000,000 |
| M7 | 詳細設計・開発完了 | 2027年11月15日 | 7ヶ月 | プログラム一式 | 56人月 | 50,400,000 |
| M8 | システムテスト完了 | 2028年3月20日 | 4.5ヶ月 | テスト結果書一式 | 18人月 | 16,200,000 |
| M9 | 研修・受入対応完了 | 2028年8月31日 | 5ヶ月 | 研修マニュアル | 10人月 | 9,000,000 |
| M10 | データ移行 | 2028年8月31日 | 5ヶ月 | コンバートデータ一式 | 8人月 | 7,200,000 |
| M11 | 本番稼働開始 | 2028年9月1日 | - |  -  | - | - |
| - | プロジェクト管理 | 全期間 | - | 各種管理資料 | 13人月 | 11,700,000 |

**合計**: 142人月 / 127,800,000円

---

## ガントチャート

```mermaid
gantt
    title 互助会会員システム スケジュール
    dateFormat YYYY-MM-DD
    axisFormat %Y-%m

    section フェーズ1: 現行調査
    プロジェクト立ち上げ           :milestone, m1, 2025-09-01, 0d
    現行システム調査                      :survey1, 2025-09-01, 2025-12-07
    業務調査・ヒアリング          :survey2, 2025-12-08, 2026-01-18
    現行調査報告書作成            :survey3, 2026-01-19, 2026-01-31
    現行調査完了                  :milestone, m2, 2026-01-31, 0d

    section フェーズ2: RFP作成
    要件整理・分析                :req1, 2026-02-01, 2026-02-28
    フィット&ギャップ分析         :req2, 2026-03-01, 2026-03-28
    移行計画策定                  :req3, 2026-03-29, 2026-04-11
    RFP作成                       :req4, 2026-04-12, 2026-04-30
    RFP作成完了                   :milestone, m3, 2026-04-30, 0d

    section フェーズ3: 提案
    システム提案書作成            :prop1, 2026-05-01, 2026-05-21
    見積作成            :prop2, 2026-05-22, 2026-06-04
    提案・プレゼンテーション      :prop3, 2026-06-05, 2026-06-15
    提案書完了                    :milestone, m4, 2026-06-15, 0d

    section フェーズ4: 要件定義
    要件定義計画・体制構築        :def1, 2026-06-16, 2026-06-29
    業務要件定義                  :def2, 2026-06-30, 2026-08-24
    システム要件定義              :def3, 2026-08-25, 2026-10-19
    移行要件定義                  :def4, 2026-10-20, 2026-11-15
    要件定義完了                  :milestone, m5, 2026-11-15, 0d

    section フェーズ5: 基本設計
    機能設計               :design1, 2026-11-16, 2027-04-04
    基本設計完了                  :milestone, m6, 2027-04-15, 0d

    section フェーズ6: 詳細設計・開発
    詳細設計                      :dev1, 2027-04-05, 2027-05-30
    プログラム開発                :dev2, 2027-05-31, 2027-10-17
    単体テスト                    :dev3, 2027-10-18, 2027-11-14
    詳細設計・開発完了            :milestone, m7, 2027-11-15, 0d

    section フェーズ7: システムテスト
    結合テスト                    :test1, 2027-11-15, 2028-01-09
    システムテスト                :test2, 2028-01-10, 2028-03-20
    システムテスト完了            :milestone, m8, 2028-03-20, 0d

    section フェーズ8: 受入テスト
    教育研修                :milestone, m10, 2028-03-31, 0d
    受入対応                :accept1, 2028-03-21, 2028-08-07
    受入テスト完了                :milestone, m10, 2028-08-07, 0d

    section フェーズ9: 移行
    データ移行          :mig1, 2028-04-01, 2028-08-31

    section フェーズ10: 稼働
    本番稼働判定            :milestone, m11, 2028-08-15, 0d
    本番稼働             :mig2, 2028-09-01, 2028-12-31
```

---

## 年度別作業概要

### 2025年度（2025年9月～2026年3月）
- **主要作業**: 現行調査・分析、RFP作成開始
- **体制**: SE2名、営業1名
- **工数**: 13人月（現行調査10人月 + RFP作成3人月）
- **予算**: 11,700,000円
- **成果物**: 現行調査報告書、要件整理資料

### 2026年度（2026年4月～2027年3月）
- **主要作業**: RFP完成～基本設計開始
- **体制**: SE1-2名、営業1名、PM1名（後半）
- **工数**: 24人月（RFP完成3人月 + 提案1人月 + 要件定義10人月 + 基本設計10人月）
- **予算**: 21,600,000円
- **成果物**: RFP、提案書、要件定義書、基本設計書

### 2027年度（2027年4月～2028年3月）
- **主要作業**: 詳細設計・開発～システムテスト完了
- **体制**: PM1名、SE3名、PG5名（段階的増員）
- **工数**: 74人月（詳細・開発・単体56人月 + システムテスト18人月）
- **予算**: 66,600,000円
- **成果物**: 詳細設計書、プログラム一式、テスト結果書

### 2028年度（2028年4月～2028年8月）
- **主要作業**: 移行・研修・受入対応～本番稼働
- **体制**: PM1名、SE2名、PG2名
- **工数**: 18人月（受入対応10人月 + 移行8人月）
- **予算**: 16,200,000円
- **成果物**: 研修マニュアル、本稼働システム

### 全期間（プロジェクト管理）
- **主要作業**: プロジェクト全体の管理・統括
- **体制**: PM1名、PL1名
- **工数**: 13人月（全期間を通じた管理業務）
- **予算**: 11,700,000円
- **成果物**: 各種管理資料

---

## 重要な判定ポイント（ゲート）

### Gate 1: 現行調査完了判定（2026年1月31日）
- **判定基準**:
  - 現行システム構成・機能の把握完了
  - 業務フロー・課題の整理完了
  - RFP作成に必要な情報収集完了
- **判定者**: 親会社プロジェクト責任者、子会社PM
- **工数**: 10人月（累計）
- **予算**: 9,000,000円（累計）

### Gate 2: 要件定義完了判定（2026年11月15日）
- **判定基準**:
  - 業務要件・システム要件の合意
  - 移行要件の確定
  - 開発規模・期間の確定
- **判定者**: 親会社業務担当者、システム担当者、子会社PM
- **工数**: 27人月（累計：現行調査10 + RFP6 + 提案1 + 要件定義10）
- **予算**: 24,300,000円（累計）

### Gate 3: 設計完了判定（2027年4月15日）
- **判定基準**:
  - 基本設計書の承認
  - 開発着手可能性の確認
  - IDCFクラウド環境設計の承認
- **判定者**: 親会社システム担当者、子会社PM・SE
- **工数**: 37人月（累計：Gate2まで27 + 基本設計10）
- **予算**: 33,300,000円（累計）

### Gate 4: 開発完了判定（2027年11月15日）
- **判定基準**:
  - 全機能の開発完了
  - 単体テスト完了
  - 結合テスト着手可能性確認
- **判定者**: 子会社PM・PL・SE
- **工数**: 93人月（累計：Gate3まで37 + 詳細・開発・単体56）
- **予算**: 83,700,000円（累計）

### Gate 5: 本番稼働判定（2028年8月15日）
- **判定基準**:
  - 全テスト完了・品質確認
  - データ移行完了・整合性確認
  - IDCFクラウド運用体制準備完了
- **判定者**: 親会社プロジェクト責任者、子会社PM
- **工数**: 142人月（累計：Gate4まで93 + システムテスト18 + 受入テスト10 + 移行8 + 管理13）
- **予算**: 127,800,000円

---

## プロジェクト管理ツール

1. **プロジェクト管理**: Google Spreadsheets
   - 進捗管理
   - リソース計画・予算管理
   - 課題管理

2. **開発管理**: Redmine
   - 詳細タスク管理
   - バグ・課題管理

3. **構成管理**: Subversion
   - 要件定義書・設計書管理
   - 議事録管理
   - プログラムコード管理

4. **IDCFクラウド管理**: IDCFクラウド管理コンソール
   - インフラ監視
   - リソース管理
   - セキュリティ管理
