# 互助会会員システムリプレースプロジェクト 全工程表

## プロジェクト概要
- **対象システム**: 互助会会員システム
- **会員規模**: 数十万会員
- **契約形態**: 現行調査・要件定義（準委任）、設計以降（請負）
- **稼働予定**: 2-3年後
- **移行方式**: 並行稼働後、一括データ移行

---

## フェーズ1: 現行調査・分析（準委任）

### 1.1 プロジェクト立ち上げ
- **期間**: 2週間
- **担当**: SE1名、営業1名
- **成果物**: 
  - プロジェクト計画書
  - 体制表・役割分担表
  - スケジュール表

#### 主要作業
- プロジェクト体制構築
- 親会社担当者との顔合わせ・役割確認
- 現行システム会社との調整・情報提供依頼
- 調査計画書作成

### 1.2 現行システム調査
- **期間**: 8週間
- **担当**: SE2名
- **成果物**: 
  - 現行システム構成図
  - 機能一覧表
  - データベース設計書（現行）
  - 連携システム一覧・連携仕様書

#### 主要作業
##### システム基盤調査（2週間）
- ハードウェア構成調査
- ソフトウェア構成調査（OS、DB、ミドルウェア）
- ネットワーク構成調査
- バックアップ・運用体制調査

##### データベース調査（3週間）
- DB設計書収集・分析
- テーブル構造調査
- データ量・増加傾向調査
- データ品質調査（データクレンジング要否判定）

##### 機能調査（2週間）
- 画面一覧・機能一覧作成
- 帳票一覧作成
- バッチ処理一覧作成
- カスタマイズ機能調査

##### 連携システム調査（1週間）
- 連携先システム特定
- 連携方式・データ形式調査
- 連携タイミング・頻度調査

### 1.3 業務調査・ヒアリング
- **期間**: 4週間
- **担当**: SE2名、営業1名
- **成果物**: 
  - 現行業務フロー図
  - 業務課題一覧
  - ヒアリング議事録

#### 主要作業
##### 業務担当者ヒアリング（3週間）
- 会員管理業務フロー確認
- 入金管理業務フロー確認
- 解約・変更業務フロー確認
- 統計・レポート業務フロー確認
- システム運用業務フロー確認

##### 課題整理（1週間）
- 現行システムの課題整理
- 業務上の課題整理
- 改善要望整理

### 1.4 現行調査報告書作成
- **期間**: 3週間
- **担当**: SE2名、営業1名
- **成果物**: 
  - 現行調査報告書
  - 課題・改善提案書

#### 主要作業
- 調査結果取りまとめ
- 課題分析・分類
- 改善提案作成
- 報告書作成・レビュー

---

## フェーズ2: RFP作成・コンサルティング（準委任）

### 2.1 要件整理・分析
- **期間**: 4週間
- **担当**: SE2名
- **成果物**: 
  - 機能要件一覧
  - 非機能要件一覧
  - システム化方針書

#### 主要作業
##### 機能要件整理（2週間）
- 現行機能の必要性評価
- 新規機能要望整理
- 機能優先度付け
- 段階的開発計画検討

##### 非機能要件整理（2週間）
- 性能要件（数十万会員対応）
- 可用性要件
- セキュリティ要件
- 運用・保守要件

### 2.2 フィット&ギャップ分析
- **期間**: 3週間
- **担当**: SE2名
- **成果物**: 
  - フィット&ギャップ分析書
  - カスタマイズ方針書
  - 開発規模見積書

#### 主要作業
- 子会社クラウドシステムとの適合性評価
- ギャップ項目抽出・分類
- カスタマイズ要否判定
- 開発工数見積

### 2.3 移行計画策定
- **期間**: 2週間
- **担当**: SE2名
- **成果物**: 
  - データ移行計画書
  - 移行リスク評価書
  - 並行稼働計画書

#### 主要作業
- データ移行方式検討
- 移行プログラム要件定義
- 並行稼働期間・方式検討
- 移行リスク分析

### 2.4 RFP作成
- **期間**: 3週間
- **担当**: SE2名、営業1名
- **成果物**: 
  - RFP（提案依頼書）
  - システム要件書
  - 調達仕様書

#### 主要作業
- RFP構成検討
- 要件書作成
- 評価基準策定
- 契約条件整理

---

## フェーズ3: 提案書作成・提案（準委任）

### 3.1 提案書作成
- **期間**: 4週間
- **担当**: SE2名、営業1名
- **成果物**: 
  - システム提案書
  - 技術提案書
  - プロジェクト計画書
  - 見積書

#### 主要作業
##### システム提案（2週間）
- システム構成提案
- 機能提案（標準機能・カスタマイズ機能）
- インフラ提案（IDCFクラウド）
- セキュリティ対策提案

##### プロジェクト提案（2週間）
- 開発体制・スケジュール提案
- 移行計画提案
- 運用・保守提案
- リスク対策提案

### 3.2 提案・プレゼンテーション
- **期間**: 1週間
- **担当**: SE2名、営業1名
- **成果物**: 
  - プレゼンテーション資料
  - 質疑応答記録

#### 主要作業
- 提案資料作成
- プレゼンテーション実施
- 質疑応答対応
- 提案内容調整

---

## フェーズ4: 要件定義（準委任）

### 4.1 要件定義体制構築
- **期間**: 1週間
- **担当**: PM1名、SE3名
- **成果物**: 
  - 要件定義計画書
  - 体制図・役割分担表

#### 主要作業
- 要件定義体制確立
- 作業計画策定
- 品質管理計画策定

### 4.2 業務要件定義
- **期間**: 6週間
- **担当**: SE3名
- **成果物**: 
  - 業務要件定義書
  - 業務フロー図
  - 帳票要件書

#### 主要作業
##### 業務要件整理（3週間）
- To-Be業務フロー策定
- 業務ルール整理
- 権限・セキュリティ要件定義

##### 帳票要件定義（2週間）
- 帳票一覧・レイアウト定義
- 統計・分析要件定義
- 外部連携要件定義

##### 要件レビュー（1週間）
- 業務担当者レビュー
- 要件調整・確定

### 4.3 システム要件定義
- **期間**: 8週間
- **担当**: SE3名
- **成果物**: 
  - システム要件定義書
  - 画面要件書
  - データベース要件書
  - 連携仕様書

#### 主要作業
##### 機能要件定義（4週間）
- 画面仕様書作成
- 処理仕様書作成
- バッチ仕様書作成

##### データ要件定義（2週間）
- データベース論理設計
- データ項目定義
- データ移行要件定義

##### 非機能要件定義（2週間）
- 性能要件詳細化
- セキュリティ要件詳細化
- 運用要件詳細化

### 4.4 移行要件定義
- **期間**: 4週間
- **担当**: SE2名
- **成果物**: 
  - データ移行要件書
  - 移行ツール要件書
  - 移行テスト計画書

#### 主要作業
- データ移行仕様策定
- 移行プログラム仕様策定
- 移行スケジュール詳細化

---

## フェーズ5: 設計・開発（請負）

### 5.1 基本設計
- **期間**: 10週間
- **担当**: PM1名、SE4名、PG2名
- **成果物**: 
  - 基本設計書
  - システム構成設計書
  - データベース設計書

#### 主要作業
##### システム設計（4週間）
- システム構成設計
- 処理方式設計
- セキュリティ設計

##### データベース設計（3週間）
- データベース物理設計
- インデックス設計
- パフォーマンス設計

##### 画面・帳票設計（3週間）
- 画面設計書作成
- 帳票設計書作成
- UI/UX設計

### 5.2 詳細設計
- **期間**: 12週間
- **担当**: SE4名、PG4名
- **成果物**: 
  - 詳細設計書
  - プログラム設計書
  - テスト仕様書

#### 主要作業
##### プログラム詳細設計（8週間）
- モジュール設計
- インターフェース設計
- 例外処理設計

##### テスト設計（4週間）
- 単体テスト仕様書作成
- 結合テスト仕様書作成
- システムテスト仕様書作成

### 5.3 プログラム開発
- **期間**: 20週間
- **担当**: SE2名、PG6名
- **成果物**: 
  - プログラム一式
  - 単体テスト結果
  - プログラム仕様書

#### 主要作業
##### 開発環境構築（2週間）
- 開発環境セットアップ
- 開発標準・規約策定
- バージョン管理環境構築

##### プログラム製造（16週間）
- アプリケーション開発
- データベース構築
- バッチプログラム開発

##### 単体テスト（2週間）
- 単体テスト実施
- バグフィックス
- プログラムレビュー

### 5.4 移行プログラム開発
- **期間**: 8週間
- **担当**: SE2名、PG2名
- **成果物**: 
  - データ移行プログラム
  - 移行ツール
  - 移行手順書

#### 主要作業
- データ抽出プログラム開発
- データ変換プログラム開発
- データ検証プログラム開発

---

## フェーズ6: テスト（請負）

### 6.1 結合テスト
- **期間**: 8週間
- **担当**: PM1名、SE3名、PG4名、テスター2名
- **成果物**: 
  - 結合テスト結果書
  - バグ管理表
  - 修正プログラム

#### 主要作業
##### システム内結合テスト（4週間）
- 機能間連携テスト
- データベース連携テスト
- バッチ処理テスト

##### システム間結合テスト（4週間）
- 外部システム連携テスト
- インターフェーステスト
- 性能テスト（基本）

### 6.2 システムテスト
- **期間**: 6週間
- **担当**: PM1名、SE2名、テスター4名
- **成果物**: 
  - システムテスト結果書
  - 性能テスト結果書
  - セキュリティテスト結果書

#### 主要作業
##### 機能テスト（3週間）
- 全機能テスト実施
- 帳票出力テスト
- エラー処理テスト

##### 非機能テスト（3週間）
- 性能・負荷テスト
- セキュリティテスト
- 可用性テスト

### 6.3 受入テスト支援
- **期間**: 4週間
- **担当**: SE2名、営業1名
- **成果物**: 
  - 受入テスト支援報告書
  - 操作手順書
  - FAQ集

#### 主要作業
- 受入テスト計画策定支援
- テストデータ作成支援
- テスト実施支援
- 不具合対応

---

## フェーズ7: 移行・稼働（請負）

### 7.1 移行準備
- **期間**: 8週間
- **担当**: PM1名、SE3名、インフラ担当1名
- **成果物**: 
  - 本番環境
  - 移行リハーサル結果
  - 切替手順書

#### 主要作業
##### 本番環境構築（4週間）
- IDCFクラウド環境構築
- アプリケーション導入
- セキュリティ設定

##### 移行リハーサル（4週間）
- データ移行リハーサル実施
- 性能確認
- 切替手順確認

### 7.2 移行・データ移行
- **期間**: 4週間
- **担当**: PM1名、SE3名、DBA1名
- **成果物**: 
  - データ移行結果報告書
  - 本番稼働確認書

#### 主要作業
##### 並行稼働開始（1週間）
- 新システム稼働開始
- 並行稼働監視
- 問題対応

##### データ移行実施（2週間）
- 本番データ移行実施
- データ整合性確認
- 移行結果検証

##### 本稼働移行（1週間）
- 旧システム停止
- 本稼働開始
- 稼働確認

### 7.3 稼働安定化
- **期間**: 4週間
- **担当**: SE2名、運用担当1名
- **成果物**: 
  - 稼働安定化報告書
  - 運用手順書
  - 障害対応手順書

#### 主要作業
- 稼働監視・問題対応
- ユーザーサポート
- 性能チューニング
- 運用手順整備

---

## フェーズ8: 運用・保守移行

### 8.1 保守体制移行
- **期間**: 2週間
- **担当**: SE1名、運用担当1名
- **成果物**: 
  - 保守引継書
  - 運用マニュアル
  - 保守契約書

#### 主要作業
- 保守体制構築
- 運用手順引継
- 保守契約締結

---

## 全体スケジュール概要

| フェーズ | 期間 | 累計期間 |
|---------|------|----------|
| 1. 現行調査・分析 | 17週間 | 17週間（約4ヶ月） |
| 2. RFP作成・コンサルティング | 12週間 | 29週間（約7ヶ月） |
| 3. 提案書作成・提案 | 5週間 | 34週間（約8ヶ月） |
| 4. 要件定義 | 19週間 | 53週間（約13ヶ月） |
| 5. 設計・開発 | 50週間 | 103週間（約2年） |
| 6. テスト | 18週間 | 121週間（約2年4ヶ月） |
| 7. 移行・稼働 | 16週間 | 137週間（約2年7ヶ月） |
| 8. 運用・保守移行 | 2週間 | 139週間（約2年8ヶ月） |

**合計期間**: 約2年8ヶ月

---

## 主要リスクと対策

### 技術的リスク
- **現行システム情報不足**: 早期の現行システム会社との調整
- **データ品質問題**: データクレンジング計画の早期策定
- **性能問題**: 性能要件の明確化と定期的な性能テスト

### プロジェクト管理リスク
- **スケジュール遅延**: バッファ期間の確保と定期的な進捗管理
- **品質問題**: 段階的な品質ゲートの設置
- **移行リスク**: 十分なリハーサルと切戻し計画

### 業務継続リスク
- **並行稼働の複雑化**: 明確な運用ルールと責任分担
- **ユーザー混乱**: 十分な教育・研修期間の確保

---

## 成果物一覧

### フェーズ1成果物（8点）
1. プロジェクト計画書
2. 現行システム構成図
3. 機能一覧表・データベース設計書
4. 連携システム仕様書
5. 現行業務フロー図
6. 業務課題一覧
7. 現行調査報告書
8. 課題・改善提案書

### フェーズ2成果物（7点）
1. 機能要件一覧・非機能要件一覧
2. システム化方針書
3. フィット&ギャップ分析書
4. カスタマイズ方針書・開発規模見積書
5. データ移行計画書・移行リスク評価書
6. 並行稼働計画書
7. RFP（提案依頼書）・システム要件書

### フェーズ3成果物（4点）
1. システム提案書・技術提案書
2. プロジェクト計画書・見積書
3. プレゼンテーション資料
4. 質疑応答記録

### フェーズ4成果物（8点）
1. 要件定義計画書・体制図
2. 業務要件定義書・業務フロー図
3. 帳票要件書
4. システム要件定義書・画面要件書
5. データベース要件書・連携仕様書
6. データ移行要件書
7. 移行ツール要件書
8. 移行テスト計画書

### フェーズ5-8成果物（15点）
1. 基本設計書・システム構成設計書
2. 詳細設計書・プログラム設計書
3. プログラム一式・プログラム仕様書
4. データ移行プログラム・移行ツール
5. 各種テスト結果書
6. 本番環境・切替手順書
7. 運用手順書・保守引継書
8. その他（手順書、マニュアル類）

**総成果物数**: 42点