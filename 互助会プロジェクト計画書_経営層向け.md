# 互助会会員システム プロジェクト計画書

## 1. プロジェクト概要

### 1.1 背景と目的
- **背景**: 数十万会員を抱える現行システムの老朽化と機能拡張の必要性
- **目的**:
  - 新規パッケージシステムの開発・販売
  - 業務効率化と顧客体験向上
  - セキュリティ強化と拡張可能なアーキテクチャ構築

### 1.2 プロジェクト範囲
- **対象システム**: 互助会会員管理システムのリプレース
- **対象範囲**: 互助会会員管理システムの開発・改善
- **非対象範囲**: 外部連携システムの改修（インターフェースのみ対応）

### 1.3 主要成果物
- 新規パッケージシステム（ソースコード含む）
- システム設計書一式
- 操作・運用マニュアル

## 2. プロジェクト体制

### 2.1 組織構成
- **親会社側**:
  - プロジェクトスポンサー（経営層）
  - プロジェクト責任者
  - 業務責任者
  - 業務担当者
  - IT担当者
- **子会社側**:
  - プロジェクトマネージャー(PM)
  - プロジェクトリーダー(PL)
  - システムエンジニア(SE)
  - プログラマー（PG）
  - 営業担当者

### 2.2 役割と責任
| 役割 | 責任範囲 | 関与度 |
|------|----------|--------|
| プロジェクトスポンサー | 予算承認・重要意思決定 | 高 |
| プロジェクト責任者 | プロジェクトの意思決定 | 高 |
| 業務責任者 | 業務要件の決定 | 高 |
| 業務担当者 | 業務要件の提供 | 高 |
| IT担当者 | 導入全般の支援 | 高 |
| プロジェクトマネージャー | プロジェクト統括 | 高 |
| プロジェクトリーダー | スコープ管理・スケジュール管理 | 高 |
| システムエンジニア | ヒアリング・システム設計 | 高 |
| プログラマー | コード開発 | 低 |
| 営業担当者 | 営業・折衝管理 | 中 |

## 3. プロジェクト計画

### 3.1 全体スケジュール
```mermaid
gantt
    title 互助会会員システムリプレース 全体スケジュール
    dateFormat YYYY-MM-DD
    axisFormat %Y-%m

    section 1: 現行調査
    現行調査 :survey, 2025-09-01, 2026-01-31

    section 2: RFP作成
    RFP作成 : rfp, 2026-02-01, 2026-04-30

    section 3: 提案
    提案 : prop, 2026-05-01, 2026-06-15

    section 4: 要件定義
    要件定義 : req, 2026-06-16, 2026-11-15

    section 5: 設計・開発・テスト
    設計・開発・テスト : design, 2026-11-16, 2028-03-31

    section 6: 受入テスト
    検証・受入 : test, 2028-04-01, 2028-08-31

    section 7: 移行・稼働
    移行・稼働 : migrate, 2028-09-01, 2028-12-31
```

### 3.2 主要マイルストーン
| マイルストーン | 開始予定日 | 終了予定日 | 期間 | 成果物 |
|---------------|------------|------------|------|--------|
| 現行調査 | 2025-09-01 | 2026-01-31 | 5ヶ月 | 現行調査報告書 |
| RFP作成 | 2026-02-01 | 2026-04-30 | 3ヶ月 | RFP・システム要件書 |
| 提案 | 2026-05-01 | 2026-06-15 | 1.5ヶ月 | 見積書一式 |
| 要件定義 | 2026-06-16 | 2026-11-15 | 5ヶ月 | 要件定義書一式 |
| 基本設計 | 2026-11-16 | 2027-04-15 | 5ヶ月 | 基本設計書一式 |
| 詳細・開発・単体 | 2027-04-16 | 2027-11-15 | 7ヶ月 | プログラム一式 |
| システムテスト | 2027-11-16 | 2028-03-31 | 4.5ヶ月 | テスト結果一式|
| 研修・受入テスト | 2028-04-01 | 2028-08-31 | 5ヶ月 | 研修マニュアル |
| データ移行 | 2028-04-01 | 2028-08-31 | 5ヶ月 | コンバートデータ一式 |
| 本番稼働開始 | 2028-09-01 |  |  |  |

## 4. リソース計画

### 4.1 人員計画
- **総工数**: 142人月
- **ピーク時体制**: PM1名 + PL1名 + SE3名 + PG5名 + 営業1名

### 4.2 予算計画

#### 4.2.1 イニシャル予算（初期開発費用）
- **イニシャル総予算**: 127,800,000円（人月単価900,000円で計算）
- **内訳**:
  - 現行調査: 9,000,000円　(10人月)
  - RFP作成: 5,400,000円　(6人月)
  - 提案: 900,000円　(1人月)
  - 要件定義: 9,000,000円　(10人月)
  - 基本設計: 9,000,000円　(10人月)
  - 詳細・開発・単体: 50,400,000円　(56人月)
  - システムテスト: 16,200,000円　(18人月)
  - 受入対応: 9,000,000円　(10人月)
  - 移行: 7,200,000円　(8人月)
  - 管理: 11,700,000円　(13人月)

#### 4.2.2 ランニング予算（運用保守費用）
- **年間ランニング費用**: 14,220,000円
- **5年間総額**: 71,100,000円

**ランニング費用内訳（年額）**:
- システム保守費用: 12,420,000円
  - 基準額: 82,800,000円（基本設計+詳細・開発・単体+システムテスト+移行の合計）
  - 保守率: 15%（82,800,000円 × 15% = 12,420,000円）
- サーバ使用料: 1,800,000円（月額150,000円 × 12ヶ月）

**5年間ランニング予算**:
| 年度 | システム保守費用 | サーバ使用料 | 年間合計 |
|------|------------------|--------------|----------|
| 1年目 | 12,420,000円 | 1,800,000円 | 14,220,000円 |
| 2年目 | 12,420,000円 | 1,800,000円 | 14,220,000円 |
| 3年目 | 12,420,000円 | 1,800,000円 | 14,220,000円 |
| 4年目 | 12,420,000円 | 1,800,000円 | 14,220,000円 |
| 5年目 | 12,420,000円 | 1,800,000円 | 14,220,000円 |
| **合計** | **62,100,000円** | **9,000,000円** | **71,100,000円** |

#### 4.2.3 総予算（イニシャル + ランニング5年分）
- **総予算**: 198,900,000円
  - イニシャル予算: 127,800,000円
  - ランニング予算（5年分）: 71,100,000円

## 5. 品質管理計画

### 5.1 品質基準（IDCFクラウド基準）
1. **可用性**:
   - SLA 99.9%（月間ダウンタイム43分以内）
   - IDCFクラウドのマルチAZ構成検討
2. **性能**:
   - レスポンスタイム: 通常処理3秒以内（90%ile）
   - スループット: 500TPS以上（ピーク時）
   - 同時接続数: 500ユーザー（ピーク時）
3. **セキュリティ**:
   - IDCFクラウドのセキュリティ機能活用
   - SSL/TLS暗号化通信
   - WAF（Web Application Firewall）検討
   - クライアント認証
   - IP制限
   - 定期的な脆弱性診断実施
4. **拡張性**:
   - クラウドネイティブ設計
   - 水平スケーリング対応
5. **運用性**:
   - 24時間365日監視体制
   - 自動バックアップ（日次）
   - ログ管理・監査証跡保持

### 5.2 品質ゲート
| ゲート | 時期 | チェック項目 |
|--------|------|--------------|
| 要件定義完了 | 2026-11 | 業務/システム要件の合意 |
| 基本設計完了 | 2027-04 | 基本設計書の承認 |
| 開発完了 | 2028-04 | テスト結果確認 |

## 6. リスク管理

### 6.1 主要リスク
- **技術リスク**: 現行システム情報不足
- **スケジュールリスク**: 開発遅延
- **移行リスク**: データ不整合

### 6.2 対策
- 現行システム情報の収集
- クリティカルパスのバッファ確保
- 移行リハーサルの複数回実施

## 7. コミュニケーション計画

### 7.1 会議体制
- **ステアリングコミッティ**: 月次（経営層向け）
- **プロジェクト進捗会議**: 週次（実務者向け）
- **技術レビュー**: 随時（設計・開発フェーズ）

### 7.2 報告体制
- 進捗報告: 週次ミーティング
- 課題管理: Redmineによるトラッキング

## 8. 変更管理

### 8.1 変更管理プロセス
1. 変更要求の提出
2. 影響度分析
3. 承認プロセス
4. 実装と検証

## 9. プロジェクト管理ツール
- **進捗管理**: Googleスプレッドシート
- **タスク・課題管理**: Redmine
- **構成管理**: Subversion

---

**承認欄**
| 役職                 | 氏名 | 署名 | 承認日 |
|----------------------|------|------|--------|
| プロジェクトスポンサー |      |      |        |
| プロジェクト責任者     |      |      |        |
| 業務責任者             |      |      |        |
| IT責任者               |      |      |        |
| プロジェクトマネージャー |      |      |        |
| プロジェクトリーダー   |      |      |        |
