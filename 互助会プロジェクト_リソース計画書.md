# 互助会会員システムリプレース リソース計画書

## 1. プロジェクト概要
- **目的**: 既存システムのリプレースによる新パッケージシステムの開発・販売
- **期間**: 2025年9月～2028年12月（約3年4ヶ月）
- **インフラ**: IDCFクラウド利用
- **特徴**:
  - セキュリティ最重視のシステム設計
  - 拡張性の高いクラウドネイティブアーキテクチャ
  - 高可用性と性能を両立

## 2. フェーズ別リソース計画

| フェーズ | 期間 | 担当者数 | 総人月 |
|---------|------|----------|--------|
| 1. 現行調査・分析 | 5ヶ月 | SE2名 | 10人月 |
| 2. RFP作成・コンサルティング | 3ヶ月 | SE2名 | 6人月 |
| 3. 提案書作成・提案 | 1.5ヶ月 | SE1名+営業1名 | 1.5人月 |
| 4. 要件定義 | 5ヶ月 | SE2名 | 10人月 |
| 5. 基本設計 | 5ヶ月 | SE2名 | 10人月 |
| 6. 詳細設計・開発・単体テスト | 7ヶ月 | SE3名+PG5名 | 56人月 |
| 7. システムテスト | 4.5ヶ月 | SE2名+PG2名 | 18人月 |
| 8. 受入テスト支援 | 5ヶ月 | SE1名+PG1名 | 10人月 |
| 9. 移行・稼働 | 4ヶ月 | SE1名+PG1名 | 8人月 |
| 10. プロジェクト管理 | 全期間 | PM1名+PL1名 | 9.5人月 |
| **合計** | 約40ヶ月 | - | **139人月** |

## 3. コスト試算（人月単価900,000円）

| フェーズ | 人月 | コスト(円) |
|---------|------|-----------|
| 1. 現行調査・分析 | 10 | 9,000,000 |
| 2. RFP作成・コンサルティング | 6 | 5,400,000 |
| 3. 提案書作成・提案 | 1.5 | 1,350,000 |
| 4. 要件定義 | 10 | 9,000,000 |
| 5. 基本設計 | 10 | 9,000,000 |
| 6. 詳細設計・開発・単体テスト | 56 | 50,400,000 |
| 7. システムテスト | 18 | 16,200,000 |
| 8. 受入テスト支援 | 10 | 9,000,000 |
| 9. 移行・稼働 | 8 | 7,200,000 |
| 10. プロジェクト管理 | 9.5 | 8,550,000 |
| **合計** | 139 | **125,100,000** |

## 4. IDCFクラウド品質基準

### 4.1 可用性基準
- **SLA**: 99.9%（月間ダウンタイム43分以内）
- **構成**: IDCFクラウドのマルチAZ構成検討
- **冗長化**:
  - ストレージ: RAID構成による冗長化

### 4.2 性能基準
- **レスポンスタイム**:
  - 通常処理: 3秒以内（90%ile）
  - 検索処理: 3秒以内（90%ile）
  - 帳票出力: 10秒以内（90%ile）
- **スループット**: 500TPS以上（ピーク時）
- **同時接続数**: 500ユーザー（ピーク時）

### 4.3 セキュリティ基準
- **ネットワークセキュリティ**:
  - IDCFクラウドのファイアウォール機能
  - WAF（Web Application Firewall）検討
  - VPN接続による専用線アクセス検討
  - クライアント認証機能
  - IP制限機能
- **データセキュリティ**:
  - SSL/TLS暗号化通信（TLS1.2以上）
  - パスワード暗号化
- **アクセス制御**:
  - ロールベースアクセス制御（RBAC）
  - 操作ログの記録・監査

### 4.4 運用基準
- **監視体制**: 24時間365日監視
- **バックアップ**:
  - 日次フルバックアップ
  - 遠隔地への複製保存
- **災害対策**:
  - RTO（目標復旧時間）: 4時間以内
  - RPO（目標復旧ポイント）: 1時間以内

## 5. ステークホルダー一覧

### 5.1 顧客側ステークホルダー
| 役割 | 責任範囲 | 関与度 | 主要タスク |
|------|----------|--------|------------|
| 経営責任者 | 予算承認・重要意思決定 | 高 | 月次進捗確認・重要判断 |
| 業務部門長 | 業務要件の決定 | 高 | 要件定義・受入テスト |
| 業務担当者 | 業務要件の提供 | 高 | 要件定義・動作確認・操作性評価 |
| IT部門長 | 導入全般の検討・運用 | 高 | 導入全般の検討・運用マニュアル作成 |

### 5.2 ベンダー側ステークホルダー
| 役割 | 責任範囲 | 関与度 | 主要タスク |
|------|----------|--------|------------|
| プロジェクトマネージャー | 全体統括 | 高 | 進捗管理・リスク管理 |
| 開発リーダー | 開発工程管理 | 高 | 開発チーム統括 |
| 営業担当 | 顧客折衝 | 中 | 契約・調整業務 |

## 6. 主要リスクと対策

### 6.1 技術的リスク
| リスク | 発生確率 | 影響度 | 対策 |
|--------|----------|--------|------|
| 現行システム情報不足 | 中 | 高 | 早期の現行システム会社との調整 |
| IDCFクラウド性能不足 | 低 | 中 | 事前性能検証・スケールアップ対応 |
| セキュリティ要件未達 | 低 | 高 | セキュリティ専門家の早期アサイン |

### 6.2 プロジェクト管理リスク
| リスク | 発生確率 | 影響度 | 対策 |
|--------|----------|--------|------|
| スケジュール遅延 | 中 | 高 | クリティカルパスのバッファ確保 |
| 品質問題 | 中 | 中 | 段階的な品質ゲートの設置 |
| 要件変更 | 高 | 中 | 変更管理プロセスの厳格化 |

### 6.3 移行リスク
| リスク | 発生確率 | 影響度 | 対策 |
|--------|----------|--------|------|
| データ移行失敗 | 中 | 高 | 十分なリハーサル期間の確保 |
| 業務継続性問題 | 低 | 高 | 並行稼働期間の設定 |
| ユーザー受入れ問題 | 中 | 中 | 十分な教育・研修期間の確保 |

## 7. 成功要因

### 7.1 技術的成功要因
- IDCFクラウドの特性を活かした設計
- クラウドネイティブアーキテクチャの採用
- 段階的な移行計画の実行

### 7.2 プロジェクト管理成功要因
- 明確な役割分担と責任範囲
- 定期的なステークホルダーとのコミュニケーション
- リスクの早期発見と対策実施

### 7.3 品質確保成功要因
- IDCFクラウドの標準機能の最大活用
- 継続的な性能監視とチューニング
- セキュリティ要件の段階的実装と検証
