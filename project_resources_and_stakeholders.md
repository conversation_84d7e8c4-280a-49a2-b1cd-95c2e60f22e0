# 互助会会員システムリプレース リソース計画書

## 1. プロジェクト概要
- **目的**: 既存システムのリプレースによる新パッケージシステムの開発・販売
- **期間**: 2025年9月～2028年12月（約2年8ヶ月）
- **特徴**: 
  - セキュリティ最重視のシステム設計
  - 拡張性の高いアーキテクチャ
  - 高可用性と性能を両立

## 2. フェーズ別リソース計画

| フェーズ | 期間 | 担当者数 | 総人月 |
|---------|------|----------|--------|
| 1. 現行調査・分析 | 17週間 | SE2名 | 8人月 |
| 2. RFP作成・コンサルティング | 12週間 | SE2名 | 6人月 |
| 3. 提案書作成・提案 | 5週間 | SE2名+営業1名 | 4人月 |
| 4. 要件定義 | 19週間 | SE3名 | 14人月 |
| 5. 設計・開発 | 50週間 | SE4名+PG6名 | 125人月 |
| 6. テスト | 18週間 | SE3名+PG4名+テスター4名 | 55人月 |
| 7. 移行・稼働 | 16週間 | SE3名+DBA1名 | 16人月 |
| 8. 運用・保守移行 | 2週間 | SE1名 | 0.5人月 |
| **合計** | 139週間 | - | **228.5人月** |

## 3. コスト試算（人月単価90万円）

| フェーズ | 人月 | コスト(万円) |
|---------|------|--------------|
| 1. 現行調査・分析 | 8 | 720 |
| 2. RFP作成・コンサルティング | 6 | 540 |
| 3. 提案書作成・提案 | 4 | 360 |
| 4. 要件定義 | 14 | 1,260 |
| 5. 設計・開発 | 125 | 11,250 |
| 6. テスト | 55 | 4,950 |
| 7. 移行・稼働 | 16 | 1,440 |
| 8. 運用・保守移行 | 0.5 | 45 |
| **合計** | 228.5 | **20,565** |

## 4. 品質基準

### 優先順位
1. **セキュリティ**
   - ISO27001準拠
   - 多層防御アーキテクチャ
   - 年次ペネトレーションテスト実施

2. **拡張性**
   - モジュール型設計
   - APIファーストアプローチ
   - プラグイン機構採用

3. **可用性**
   - 99.99% SLA保証
   - マルチAZ構成
   - 自動フェイルオーバー

4. **性能**
   - 1000TPS以上（基幹処理）
   - レスポンスタイム2秒以内（90%ile）
   - 同時接続数1000以上

## 5. ステークホルダー一覧

### 顧客側ステークホルダー
| 役割 | 責任範囲 | 関与度 |
|------|----------|--------|
| 経営責任者 | 予算承認・重要意思決定 | 高 |
| 業務部門長 | 業務要件の決定 | 高 |
| IT部門長 | 技術要件の決定 | 高 |
| システム管理者 | 運用要件の定義 | 中 |
| エンドユーザー代表 | UI/UX要件の提供 | 低 |

### ベンダー側ステークホルダー
| 役割 | 責任範囲 | 関与度 |
|------|----------|--------|
| プロジェクトマネージャー | 全体統括 | 高 |
| システムアーキテクト | 技術設計 | 高 |
| セキュリティスペシャリスト | セキュリティ設計 | 高 |
| 開発リーダー | 開発工程管理 | 中 |
| 品質保証担当 | テスト計画・実施 | 中 |
| 営業担当 | 顧客折衝 | 低 |

## 6. 主要リスクと対策
- **技術的リスク**: セキュリティ専門家の早期アサイン
- **スケジュールリスク**: クリティカルパスのバッファ確保
- **品質リスク**: 段階的な品質ゲートの設置
- **移行リスク**: 十分なリハーサル期間の確保
