---
puppeteer:
  displayHeaderFooter: true
  # headerTemplate: "<div style='width: 100%; height: 0;'></div>"
  # headerTemplate: "<div style='font-size: 10px; text-align: left; width: 100%;padding-left: 1cm;'><span class='date'></span></div>"
  headerTemplate: "<div style='font-size: 10px; text-align: left; width: 100%;padding-left: 1cm;'></div>"
  footerTemplate: "<div style='font-size: 10px; text-align: center; width: 100%;'><span class='pageNumber'></span> / <span class='totalPages'></span></div>"
  margin:
    top: "1cm"
    right: "1cm"
    bottom: "0.5cm"
    left: "1cm"
  format: "A4"
  printBackground: true
  landscape: false
---
# 互助会会員システムリプレース マイルストーン（2025年9月開始）

## 主要マイルストーン一覧

| No | マイルストーン | 完了予定日 | 期間 | 主要成果物 |
|----|---------------|------------|------|------------|
| M1 | プロジェクト開始 | 2025年9月1日 | - | プロジェクト計画書 |
| M2 | 現行調査完了 | 2026年1月31日 | 5ヶ月 | 現行調査報告書 |
| M3 | RFP作成完了 | 2026年4月30日 | 3ヶ月 | RFP・システム要件書 |
| M4 | 提案書完了 | 2026年6月15日 | 1.5ヶ月 | システム提案書・見積書 |
| M5 | 要件定義完了 | 2026年11月15日 | 5ヶ月 | 要件定義書一式 |
| M6 | 基本設計完了 | 2027年4月15日 | 5ヶ月 | 基本設計書一式 |
| M7 | 詳細設計完了 | 2027年9月15日 | 5ヶ月 | 詳細設計書一式 |
| M8 | 開発完了 | 2028年4月15日 | 7ヶ月 | プログラム一式 |
| M9 | テスト完了 | 2028年8月31日 | 4.5ヶ月 | テスト結果書一式 |
| M10 | 本番稼働開始 | 2028年12月31日 | 4ヶ月 | 本稼働確認書 |

---

## ガントチャート

```mermaid
gantt
    title 互助会会員システムリプレース スケジュール
    dateFormat YYYY-MM-DD
    axisFormat %Y-%m
    
    section フェーズ1: 現行調査
    プロジェクト立ち上げ           :milestone, m1, 2025-09-01, 0d
    現行システム調査              :active, survey1, 2025-09-01, 2025-11-30
    業務調査・ヒアリング          :survey2, 2025-12-01, 2026-01-15
    現行調査報告書作成            :survey3, 2026-01-16, 2026-01-31
    現行調査完了                  :milestone, m2, 2026-01-31, 0d
    
    section フェーズ2: RFP作成
    要件整理・分析                :req1, 2026-02-01, 2026-03-15
    フィット&ギャップ分析         :req2, 2026-03-16, 2026-04-15
    RFP作成                       :req3, 2026-04-16, 2026-04-30
    RFP作成完了                   :milestone, m3, 2026-04-30, 0d
    
    section フェーズ3: 提案
    提案書作成                    :prop1, 2026-05-01, 2026-06-10
    提案・プレゼンテーション      :prop2, 2026-06-11, 2026-06-15
    提案書完了                    :milestone, m4, 2026-06-15, 0d
    
    section フェーズ4: 要件定義
    業務要件定義                  :def1, 2026-06-16, 2026-08-31
    システム要件定義              :def2, 2026-09-01, 2026-10-31
    移行要件定義                  :def3, 2026-11-01, 2026-11-15
    要件定義完了                  :milestone, m5, 2026-11-15, 0d
    
    section フェーズ5: 設計・開発
    基本設計                      :design1, 2026-11-16, 2027-04-15
    基本設計完了                  :milestone, m6, 2027-04-15, 0d
    詳細設計                      :design2, 2027-04-16, 2027-09-15
    詳細設計完了                  :milestone, m7, 2027-09-15, 0d
    プログラム開発                :dev1, 2027-09-16, 2028-04-15
    開発完了                      :milestone, m8, 2028-04-15, 0d
    
    section フェーズ6: テスト
    結合テスト                    :test1, 2028-04-16, 2028-06-30
    システムテスト                :test2, 2028-07-01, 2028-08-15
    受入テスト支援                :test3, 2028-08-16, 2028-08-31
    テスト完了                    :milestone, m9, 2028-08-31, 0d
    
    section フェーズ7: 移行・稼働
    移行準備                      :mig1, 2028-09-01, 2028-10-31
    移行・データ移行              :mig2, 2028-11-01, 2028-12-15
    稼働安定化                    :mig3, 2028-12-16, 2028-12-31
    本番稼働開始                  :milestone, m10, 2028-12-31, 0d
```

---

## 年度別作業概要

### 2025年度（2025年9月～2026年3月）
- **主要作業**: 現行調査・分析
- **体制**: SE2名、営業1名
- **成果物**: 現行調査報告書、課題・改善提案書

### 2026年度（2026年4月～2027年3月）
- **主要作業**: RFP作成～要件定義完了
- **体制**: SE2-3名、営業1名、PM1名（後半）
- **成果物**: RFP、提案書、要件定義書一式

### 2027年度（2027年4月～2028年3月）
- **主要作業**: 基本設計～開発（前半）
- **体制**: PM1名、SE4名、PG6名（段階的増員）
- **成果物**: 基本設計書、詳細設計書

### 2028年度（2028年4月～2028年12月）
- **主要作業**: 開発完了～本番稼働
- **体制**: PM1名、SE3名、PG4名、テスター4名
- **成果物**: プログラム一式、本稼働システム

---

## 四半期別マイルストーン

### 2025年度 マイルストーン
```mermaid
timeline
    title 2025年度 マイルストーン
    
    2025 Q3 : プロジェクト開始
            : 現行システム調査開始
    
    2025 Q4 : 現行システム調査継続
            : 業務調査・ヒアリング開始
```

### 2026年度 マイルストーン
```mermaid
timeline
    title 2026年度 マイルストーン
    
    2026 Q1 : 現行調査完了
            : RFP作成開始
    
    2026 Q2 : RFP完了
            : 提案完了
            : 要件定義開始
    
    2026 Q3 : 業務要件定義
            : システム要件定義開始
    
    2026 Q4 : システム要件定義完了
            : 移行要件定義
            : 要件定義完了
```

### 2027年度 マイルストーン
```mermaid
timeline
    title 2027年度 マイルストーン
    
    2027 Q1 : 基本設計開始
            : システム構成設計
    
    2027 Q2 : 基本設計完了
            : 詳細設計開始
    
    2027 Q3 : 詳細設計継続
            : プログラム設計
    
    2027 Q4 : 詳細設計完了
            : プログラム開発開始
```

### 2028年度 マイルストーン
```mermaid
timeline
    title 2028年度 マイルストーン
    
    2028 Q1 : プログラム開発継続
            : 移行プログラム開発
    
    2028 Q2 : 開発完了
            : 結合テスト開始
    
    2028 Q3 : システムテスト
            : 受入テスト支援
            : テスト完了
    
    2028 Q4 : 移行準備
            : データ移行
            : 本番稼働開始
```

---

## 重要な判定ポイント（ゲート）

### Gate 1: 現行調査完了判定（2026年1月31日）
- **判定基準**: 
  - 現行システム構成・機能の把握完了
  - 業務フロー・課題の整理完了
  - RFP作成に必要な情報収集完了
- **判定者**: 親会社プロジェクト責任者、子会社PM

### Gate 2: 要件定義完了判定（2026年11月15日）
- **判定基準**:
  - 業務要件・システム要件の合意
  - 移行要件の確定
  - 開発規模・期間の確定
- **判定者**: 親会社業務担当者、システム担当者、子会社PM

### Gate 3: 設計完了判定（2027年9月15日）
- **判定基準**:
  - 詳細設計書の承認
  - 開発着手可能性の確認
  - テスト計画の承認
- **判定者**: 親会社システム担当者、子会社PM・SE

### Gate 4: 開発完了判定（2028年4月15日）
- **判定基準**:
  - 全機能の開発完了
  - 単体テスト完了
  - 結合テスト着手可能性確認
- **判定者**: 子会社PM・SE、品質管理責任者

### Gate 5: 本番稼働判定（2028年12月31日）
- **判定基準**:
  - 全テスト完了・品質確認
  - データ移行完了・整合性確認
  - 運用体制準備完了
- **判定者**: 親会社プロジェクト責任者、子会社PM

---

## リスク管理マイルストーン

### フェーズ1-2: 現行調査・RFP作成段階
```mermaid
flowchart LR
    A[プロジェクト開始<br/>2025年9月] --> B{現行システム情報<br/>取得可能?}
    B -->|Yes| C[現行調査継続]
    B -->|No| D[情報取得計画見直し]
    D --> C
    C --> E[現行調査完了<br/>2026年1月]
    E --> F[RFP作成<br/>2026年4月]
```

### フェーズ3-4: 提案・要件定義段階
```mermaid
flowchart LR
    A[提案開始<br/>2026年5月] --> B{親会社との<br/>要件合意可能?}
    B -->|Yes| C[要件定義開始]
    B -->|No| D[提案内容調整]
    D --> C
    C --> E{詳細要件<br/>合意取得?}
    E -->|Yes| F[要件定義完了<br/>2026年11月]
    E -->|No| G[要件調整期間]
    G --> F
```

### フェーズ5: 設計・開発段階
```mermaid
flowchart LR
    A[基本設計開始<br/>2026年11月] --> B{アーキテクチャ<br/>性能要件達成?}
    B -->|Yes| C[詳細設計開始]
    B -->|No| D[設計見直し]
    D --> C
    C --> E{開発規模<br/>計画通り?}
    E -->|Yes| F[開発完了<br/>2028年4月]
    E -->|No| G[開発計画調整]
    G --> F
```

### フェーズ6-7: テスト・移行段階
```mermaid
flowchart LR
    A[テスト開始<br/>2028年4月] --> B{品質基準<br/>達成?}
    B -->|Yes| C[移行準備開始]
    B -->|No| D[品質改善期間]
    D --> C
    C --> E{移行リハーサル<br/>成功?}
    E -->|Yes| F[本番稼働<br/>2028年12月]
    E -->|No| G[移行計画見直し]
    G --> F
```

---

## プロジェクト管理ツール

1. **プロジェクト管理**: Google spreadsheets
   - 進捗管理
   - リソース計画・予算管理
   - 課題管理

2. **開発管理**: Redmine
   - 詳細タスク管理
   - バグ・課題管理

3. **構成管理**: Subversion
   - 要件定義書・設計書管理
   - 議事録管理
   - プログラムコード管理

この組み合わせにより、プロジェクトの規模と複雑さに対応できる管理体制を構築できます。